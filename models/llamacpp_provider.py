"""
LLaMA.cpp model provider implementation.
"""
from typing import Any, Dict, List, Optional, Union, Mapping, Iterator, AsyncIterator
import requests
import json

from langchain.llms.base import LLM
from langchain.callbacks.manager import CallbackManagerForLLMRun
from langchain.tools import BaseTool
from langchain.schema.runnable import RunnableConfig
from langchain.chat_models.base import BaseChatModel
from langchain.schema.messages import (BaseMessage, AIMessage, AIMessageChunk,
                                      SystemMessage, HumanMessage, ToolMessage)
from langchain.schema.output import ChatGeneration, ChatGenerationChunk, ChatResult


class LlamaCppChatModel(BaseChatModel):
    """LLaMA.cpp Chat Model implementation."""

    api_url: str
    temperature: float = 0.7
    max_tokens: int = 2048
    tools: List[BaseTool] = []
    tool_choice: Optional[Union[str, Dict]] = None

    @property
    def _llm_type(self) -> str:
        return "llama.cpp.chat"

    def bind_tools(self, tools: List[BaseTool], tool_choice: Optional[Union[str, Dict]] = None, **kwargs: Any) -> BaseChatModel:
        """Bind tools to the chat model.

        Args:
            tools: The tools to bind to the chat model
            tool_choice: The tool choice to use ("auto", "any", or specific tool name)

        Returns:
            The chat model with tools bound
        """
        # Create a copy of the chat model with the tools bound
        new_instance = self.copy()
        new_instance.tools = tools
        new_instance.tool_choice = tool_choice
        return new_instance

    def with_config(self, config: RunnableConfig) -> BaseChatModel:
        """Return a new chat model with the specified config."""
        new_instance = self.copy()
        if "tools" in config:
            new_instance.tools = config["tools"]
        return new_instance

    def copy(self) -> BaseChatModel:
        """Create a copy of the chat model."""
        return LlamaCppChatModel(
            api_url=self.api_url,
            temperature=self.temperature,
            max_tokens=self.max_tokens,
            tools=self.tools.copy() if self.tools else [],
            tool_choice=self.tool_choice)

    def _generate(
        self,
        messages: List[BaseMessage],
        stop: Optional[List[str]] = None,
        run_manager = None,
        **kwargs: Any,
    ) -> ChatResult:
        """Call the LLaMA.cpp API and generate a chat result."""
        headers = {"Content-Type": "application/json"}

        # Convert messages to a prompt format that llama.cpp can understand
        prompt = self._convert_messages_to_prompt(messages)

        # Format the request according to llama.cpp server API
        data = {
            "prompt": prompt,
            "n_predict": min(self.max_tokens, 200),  # Limit response length
            "temperature": max(self.temperature, 0.3),  # Ensure some randomness
            "stop": stop if stop else [
                "User:", "System:", "\n\nUser:", "\n\nThought:",
                "\n\nQuestion:", "Response:", "\n\nResponse:",
                "QUESTION:", "PREVIOUS", "AVAILABLE"
            ],
            "stream": False,
            "repeat_penalty": 1.2,  # Stronger repetition penalty
            "top_p": 0.8,  # More focused responses
            "top_k": 30,   # Limit vocabulary more
            "frequency_penalty": 0.1,  # Reduce repetition
        }

        # Add any additional parameters
        for key, value in kwargs.items():
            # Map common parameters to llama.cpp equivalents
            if key == "max_tokens":
                data["n_predict"] = value
            elif key == "top_p":
                data["top_p"] = value
            elif key == "top_k":
                data["top_k"] = value
            else:
                data[key] = value

        try:
            response = requests.post(self.api_url, headers=headers, data=json.dumps(data))
            response.raise_for_status()
            response_json = response.json()

            # Extract the generated content from the response
            content = ""
            if "content" in response_json:
                content = response_json["content"]
            elif "response" in response_json:
                # Some versions return under 'response' key
                content = response_json["response"]
            elif "text" in response_json:
                # Some versions return under 'text' key
                content = response_json["text"]
            elif "choices" in response_json and len(response_json["choices"]) > 0:
                # OpenAI-compatible format
                content = response_json["choices"][0]["text"]
            else:
                # If we can't find a known response format, return the raw response
                content = str(response_json)

            # Clean up the content to remove any tool descriptions or repeated prompts
            content = self._clean_response_content(content)

            # Create a ChatGeneration with the content
            generation = ChatGeneration(
                message=AIMessage(content=content),
            )

            # Return a ChatResult with the generation
            return ChatResult(generations=[generation])
        except Exception as e:
            raise ValueError(f"Error calling LLaMA.cpp API: {str(e)}")

    def _clean_response_content(self, content: str) -> str:
        """Clean up the response content to remove tool descriptions and repeated prompts."""
        # Remove any tool descriptions
        if "Available Tools:" in content:
            content = content.split("Available Tools:")[0].strip()

        # Remove any repeated conversation
        if "User:" in content:
            content = content.split("User:")[0].strip()

        # Remove any assistant prefix
        if content.startswith("Assistant:"):
            content = content[len("Assistant:"):].strip()

        return content

    def _convert_messages_to_prompt(self, messages: List[BaseMessage]) -> str:
        """Convert a list of messages to a prompt format that llama.cpp can understand."""
        prompt = """You are a helpful AI assistant. Answer the user's questions directly and concisely.

"""

        # Add tool specifications if tools are bound
        if hasattr(self, "tools") and self.tools:
            prompt += "You have access to the following tools:\n"
            for tool in self.tools:
                prompt += f"- {tool.name}: {tool.description}\n"
            prompt += "\nOnly use these tools when necessary. Respond directly when you know the answer.\n\n"

        # Add the conversation history
        for message in messages:
            if isinstance(message, SystemMessage):
                prompt += f"System: {message.content}\n\n"
            elif isinstance(message, HumanMessage):
                prompt += f"User: {message.content}\n\n"
            elif isinstance(message, AIMessage):
                prompt += f"Assistant: {message.content}\n\n"
            elif isinstance(message, ToolMessage):
                prompt += f"Tool ({message.tool_call_id}): {message.content}\n\n"

        prompt += "Assistant: "
        return prompt

    def _stream(
        self,
        messages: List[BaseMessage],
        stop: Optional[List[str]] = None,
        run_manager = None,
        **kwargs: Any,
    ) -> Iterator[ChatGenerationChunk]:
        """Stream the response from the LLaMA.cpp API."""
        # For now, we'll implement a simple non-streaming version that returns a single chunk
        # This is a workaround until we implement proper streaming
        result = self._generate(messages, stop, run_manager, **kwargs)
        if result.generations:
            yield ChatGenerationChunk(message=AIMessageChunk(content=result.generations[0].message.content))

    async def _astream(
        self,
        messages: List[BaseMessage],
        stop: Optional[List[str]] = None,
        run_manager = None,
        **kwargs: Any,
    ) -> AsyncIterator[ChatGenerationChunk]:
        """Async stream the response from the LLaMA.cpp API."""
        # For now, we'll implement a simple non-streaming version that returns a single chunk
        # This is a workaround until we implement proper streaming
        result = self._generate(messages, stop, run_manager, **kwargs)
        if result.generations:
            yield ChatGenerationChunk(message=AIMessageChunk(content=result.generations[0].message.content))

class LlamaCppProvider:
    """LLaMA.cpp model provider implementation."""

    def __init__(self, config: Dict[str, Any], api_url: Optional[str] = None):
        """Initialize the LLaMA.cpp model provider."""
        self.config = config
        self.api_url = api_url or "http://172.16.0.111:11111/completion"

    def get_llm(self) -> LlamaCppChatModel:
        """Get the LLaMA.cpp language model instance."""
        return LlamaCppChatModel(
            api_url=self.api_url,
            temperature=self.config.get("temperature", 0.7),
            max_tokens=self.config.get("max_tokens", 512)  # Reduced for faster responses
        )
