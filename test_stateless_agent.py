#!/usr/bin/env python3
"""
Test script for the Stateless ReAct Agent

This script demonstrates the stateless agent functionality and compares it
with the existing stateful agents (react_agent.py and react_agent_gemini.py).
"""
import os
from dotenv import load_dotenv

# Load environment variables
if os.path.exists(".env"):
    load_dotenv()


def test_stateless_agent():
    """Test the stateless ReAct agent."""
    print("🧪 TESTING STATELESS REACT AGENT")
    print("=" * 60)
    
    try:
        from config.config import Config
        from models import get_model_provider
        from mcps.tools_manager import MCPToolsManager
        from core.stateless_react_agent import StatelessReActAgent
        
        # Initialize components
        print("🔧 Initializing components...")
        config = Config()
        model_provider = get_model_provider(config.model_provider)
        llm = model_provider.get_llm()
        
        tools_manager = MCPToolsManager()
        tools = tools_manager.get_tools()
        
        # Create stateless agent
        agent = StatelessReActAgent(
            llm=llm,
            tools=tools,
            max_iterations=3
        )
        
        print(f"✅ Stateless agent initialized with {len(tools)} tools")
        print(f"🔧 Available tools: {agent.get_tool_names()}")
        
        # Test cases
        test_cases = [
            "What's the current date?",
            "Calculate 15 + 27",
            "What time is it?",
            "Search for information about Python programming",
        ]
        
        print("\n🎯 RUNNING TEST CASES")
        print("-" * 40)
        
        for i, test_input in enumerate(test_cases, 1):
            print(f"\n📝 Test Case {i}: {test_input}")
            print("=" * 50)
            
            try:
                result = agent.process(test_input)
                print(f"✅ Result: {result}")
            except Exception as e:
                print(f"❌ Error: {e}")
        
        # Test with context
        print(f"\n🔍 TESTING WITH CONTEXT")
        print("-" * 40)
        
        context = "The user is working on a Python project and needs help with calculations."
        test_with_context = "What's 25 * 4?"
        
        print(f"📝 Question: {test_with_context}")
        print(f"📋 Context: {context}")
        
        try:
            result = agent.process(test_with_context, context=context)
            print(f"✅ Result with context: {result}")
        except Exception as e:
            print(f"❌ Error: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ Stateless agent test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


def compare_agents():
    """Compare stateless agent with stateful agents."""
    print("\n🔄 COMPARING AGENT IMPLEMENTATIONS")
    print("=" * 60)
    
    try:
        from config.config import Config
        from models import get_model_provider
        from mcps.tools_manager import MCPToolsManager
        from memory import ReActMemoryManager
        from core import ReActAgent
        from core.react_agent_gemini import ReActAgent as GeminiReActAgent
        from core.stateless_react_agent import StatelessReActAgent
        
        # Initialize common components
        config = Config()
        model_provider = get_model_provider(config.model_provider)
        llm = model_provider.get_llm()
        
        tools_manager = MCPToolsManager()
        tools = tools_manager.get_tools()
        
        # Initialize memory for stateful agents
        memory = ReActMemoryManager("test_comparison_memory.db")
        
        # Create all three agents
        stateful_agent = ReActAgent(llm=llm, tools=tools, memory_manager=memory, max_iterations=3)
        gemini_agent = GeminiReActAgent(llm=llm, tools=tools, memory_manager=memory, max_iterations=3)
        stateless_agent = StatelessReActAgent(llm=llm, tools=tools, max_iterations=3)
        
        agents = {
            "Stateful ReAct Agent": stateful_agent,
            "Gemini ReAct Agent": gemini_agent,
            "Stateless ReAct Agent": stateless_agent
        }
        
        test_question = "What's the current date?"
        
        print(f"📝 Test Question: {test_question}")
        print("-" * 40)
        
        for agent_name, agent in agents.items():
            print(f"\n🤖 Testing {agent_name}:")
            try:
                if agent_name == "Stateless ReAct Agent":
                    result = agent.process(test_question)
                else:
                    result = agent.process(test_question)
                print(f"✅ {agent_name} Result: {result[:100]}...")
            except Exception as e:
                print(f"❌ {agent_name} Error: {e}")
        
        # Clean up test memory
        memory.clear()
        
        return True
        
    except Exception as e:
        print(f"❌ Agent comparison failed: {e}")
        import traceback
        traceback.print_exc()
        return False


def demonstrate_stateless_benefits():
    """Demonstrate the benefits of stateless agents."""
    print("\n🌟 DEMONSTRATING STATELESS AGENT BENEFITS")
    print("=" * 60)
    
    try:
        from config.config import Config
        from models import get_model_provider
        from mcps.tools_manager import MCPToolsManager
        from core.stateless_react_agent import StatelessReActAgent
        import threading
        import time
        
        # Initialize components
        config = Config()
        model_provider = get_model_provider(config.model_provider)
        llm = model_provider.get_llm()
        
        tools_manager = MCPToolsManager()
        tools = tools_manager.get_tools()
        
        # Create stateless agent
        agent = StatelessReActAgent(llm=llm, tools=tools, max_iterations=2)
        
        print("🔄 Benefit 1: Thread Safety - Multiple concurrent requests")
        print("-" * 50)
        
        def process_request(request_id, question):
            """Process a request in a separate thread."""
            print(f"🧵 Thread {request_id} starting: {question}")
            try:
                result = agent.process(question)
                print(f"✅ Thread {request_id} completed: {result[:50]}...")
            except Exception as e:
                print(f"❌ Thread {request_id} error: {e}")
        
        # Simulate concurrent requests
        questions = [
            "What's 10 + 5?",
            "What's the current time?",
            "Calculate 20 * 3"
        ]
        
        threads = []
        for i, question in enumerate(questions, 1):
            thread = threading.Thread(target=process_request, args=(i, question))
            threads.append(thread)
            thread.start()
        
        # Wait for all threads to complete
        for thread in threads:
            thread.join()
        
        print("\n🔄 Benefit 2: No State Pollution - Independent contexts")
        print("-" * 50)
        
        # Test with different contexts
        contexts = [
            ("Math context", "The user is a student learning basic arithmetic."),
            ("Business context", "The user is a business analyst working on financial calculations."),
            ("Programming context", "The user is a developer working on algorithm optimization.")
        ]
        
        question = "What's 15 + 25?"
        
        for context_name, context in contexts:
            print(f"\n📋 {context_name}:")
            result = agent.process(question, context=context)
            print(f"✅ Result: {result[:80]}...")
        
        print("\n🔄 Benefit 3: Easy Scaling - No memory management overhead")
        print("-" * 50)
        print("✅ Stateless agents can be easily deployed in:")
        print("  - Serverless functions (AWS Lambda, Azure Functions)")
        print("  - Container orchestration (Kubernetes)")
        print("  - Load-balanced environments")
        print("  - Microservices architectures")
        
        return True
        
    except Exception as e:
        print(f"❌ Benefits demonstration failed: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """Main test function."""
    print("🚀 STATELESS REACT AGENT TEST SUITE")
    print("=" * 60)
    
    results = []
    
    # Test 1: Basic functionality
    results.append(test_stateless_agent())
    
    # Test 2: Compare with existing agents
    results.append(compare_agents())
    
    # Test 3: Demonstrate benefits
    results.append(demonstrate_stateless_benefits())
    
    # Summary
    print("\n📊 TEST SUMMARY")
    print("=" * 30)
    passed = sum(results)
    total = len(results)
    
    print(f"✅ Tests passed: {passed}/{total}")
    
    if passed == total:
        print("🎉 All tests passed! Stateless ReAct Agent is working correctly.")
    else:
        print("⚠️ Some tests failed. Please check the output above.")
    
    return passed == total


if __name__ == "__main__":
    main()
