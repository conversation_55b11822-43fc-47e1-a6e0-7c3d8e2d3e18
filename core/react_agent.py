"""
Core ReAct Agent Implementation

Implements the ReAct (Reasoning + Acting) pattern:
User Input → Agent + Memory → Reasoning → Action → Tool Execution → 
Observation → Memory Update → Repeat until Final Answer
"""
import re
from typing import Dict, Any, List, Optional
from dataclasses import dataclass
from enum import Enum

from logger.get_logger import log


class ActionType(Enum):
    """Types of actions the agent can take."""
    TOOL_CALL = "tool_call"
    FINAL_ANSWER = "final_answer"


@dataclass
class ReActStep:
    """Represents a single step in the ReAct reasoning process."""
    thought: str
    action_type: ActionType
    action: Optional[str] = None
    action_input: Optional[str] = None
    observation: Optional[str] = None
    step_number: int = 0


class ReActAgent:
    """
    Core ReAct Agent following the specified flow pattern.
    
    Modular design allows easy extension of:
    - Tools (via tools interface)
    - Memory (via memory interface) 
    - LLM providers (via models interface)
    """
    
    def __init__(self, llm, tools: Dict[str, Any], memory_manager, max_iterations: int = 5):
        """
        Initialize ReAct Agent.
        
        Args:
            llm: Language model instance
            tools: Dictionary of available tools {name: tool_object}
            memory_manager: Memory management instance
            max_iterations: Maximum reasoning iterations
        """
        self.llm = llm
        self.tools = tools
        self.memory_manager = memory_manager
        self.max_iterations = max_iterations
        
        # Create tool descriptions for prompts
        self.tool_descriptions = self._create_tool_descriptions()
        
    def _create_tool_descriptions(self) -> str:
        """Create formatted tool descriptions for the prompt."""
        descriptions = []
        for name, tool in self.tools.items():
            if hasattr(tool, 'description'):
                descriptions.append(f"- {name}: {tool.description}")
            else:
                descriptions.append(f"- {name}: Available tool")
        return "\n".join(descriptions)
    
    @log
    def process(self, user_input: str) -> str:
        """
        Main processing method implementing the ReAct flow.
        
        Flow:
        1. Load memory context
        2. Iterative reasoning loop:
           - Agent reasoning with context
           - Action decision (tool call or final answer)
           - Tool execution and observation (if tool call)
           - Memory update with reasoning step
        3. Save final conversation to memory
        
        Args:
            user_input: User's input/question
            
        Returns:
            Final answer from the agent
        """
        print(f"\n🤖 ReAct Agent Processing: {user_input}")
        print("=" * 60)
        
        # Step 1: Load memory context
        memory_context = self._load_memory_context(user_input)
        
        # Step 2: Initialize reasoning process
        reasoning_steps = []
        iteration = 0
        
        while iteration < self.max_iterations:
            iteration += 1
            print(f"\n🔄 Iteration {iteration}/{self.max_iterations}")
            print("-" * 30)
            
            # Step 3: Agent reasoning with current context
            step = self._agent_reasoning(user_input, memory_context, reasoning_steps, iteration)
            reasoning_steps.append(step)
            
            print(f"💭 Thought: {step.thought}")
            
            # Step 4: Action decision and execution
            if step.action_type == ActionType.TOOL_CALL:
                print(f"🔧 Action: {step.action}")
                print(f"📝 Input: {step.action_input}")
                
                # Execute tool and observe
                observation = self._execute_tool(step.action, step.action_input)
                step.observation = observation
                
                print(f"👁️ Observation: {observation}")
                
                # Update memory with reasoning step
                self._update_memory_with_step(memory_context, step)
                
            elif step.action_type == ActionType.FINAL_ANSWER:
                print(f"✅ Final Answer: {step.action}")
                
                # Save complete conversation to memory and return
                self._save_conversation_to_memory(user_input, step.action, reasoning_steps)
                return step.action
        
        # If max iterations reached without final answer
        final_answer = "I've reached the maximum number of reasoning steps. Based on my analysis, I'll provide the best answer I can."
        self._save_conversation_to_memory(user_input, final_answer, reasoning_steps)
        return final_answer
    
    def _load_memory_context(self, user_input: str) -> str:
        """Load relevant memory context for the current input."""
        print("🧠 Loading memory context...")
        
        memory_vars = self.memory_manager.load_memory_variables({"input": user_input})
        return memory_vars.get("chat_history", "")
    
    def _agent_reasoning(self, user_input: str, memory_context: str, 
                        reasoning_steps: List[ReActStep], iteration: int) -> ReActStep:
        """
        Core agent reasoning step.
        
        Args:
            user_input: Original user input
            memory_context: Current memory context
            reasoning_steps: Previous reasoning steps
            iteration: Current iteration number
            
        Returns:
            ReActStep with agent's reasoning and action decision
        """
        # Build context for LLM
        context = self._build_reasoning_context(user_input, memory_context, reasoning_steps)
        
        # Get LLM response
        llm_response = self.llm.invoke(context)

        # Extract text content from response
        if hasattr(llm_response, 'content'):
            response_text = llm_response.content
        elif hasattr(llm_response, 'text'):
            response_text = llm_response.text
        else:
            response_text = str(llm_response)

        # Validate response length to prevent runaway generation
        if len(response_text) > 1000:
            print(f"⚠️ LLM response too long ({len(response_text)} chars), truncating...")
            response_text = response_text[:1000]

        # Parse LLM response into ReAct step
        return self._parse_llm_response(response_text, iteration)
    
    def _build_reasoning_context(self, user_input: str, memory_context: str,
                                reasoning_steps: List[ReActStep]) -> str:
        """Build a simple, direct prompt optimized for 8B models."""

        # Skip complex reasoning for first iteration - use direct mapping
        if not reasoning_steps:
            return self._build_direct_tool_prompt(user_input)

        # For subsequent iterations, use minimal context
        return self._build_followup_prompt(user_input, reasoning_steps[-1])

    def _build_direct_tool_prompt(self, user_input: str) -> str:
        """Direct tool selection without complex reasoning."""

        question_lower = user_input.lower()

        if "date" in question_lower or "today" in question_lower:
            return f"""Get today's date.

Question: {user_input}
Action: get_current_date
Input: """

        elif "time" in question_lower:
            return f"""Get current time.

Question: {user_input}
Action: get_current_time
Input: """

        elif any(op in user_input for op in ["+", "-", "*", "/", "calculate", "="]):
            math_expr = self._extract_math_expression(user_input)
            return f"""Calculate: {math_expr}

Question: {user_input}
Action: math_calculator
Input: {math_expr}"""

        else:
            return f"""Question: {user_input}
Action: search_web
Input: {user_input}"""

    def _extract_math_expression(self, text: str) -> str:
        """Extract mathematical expression from text."""
        # Remove common words
        text = text.replace("Calculate", "").replace("calculate", "")
        text = text.replace("What is", "").replace("what is", "")
        text = text.strip()
        return text if any(op in text for op in ["+", "-", "*", "/"]) else text

    def _build_followup_prompt(self, user_input: str, last_step: ReActStep) -> str:
        """Build prompt for final answer after tool execution."""

        if last_step.observation:
            return f"""Question: {user_input}
Tool result: {last_step.observation}

Provide final answer:
Action: Final Answer
Input: """

        return f"""Question: {user_input}
Action: Final Answer
Input: I cannot answer this question."""
    
    def _format_reasoning_steps(self, steps: List[ReActStep]) -> str:
        """Format previous reasoning steps for context."""
        if not steps:
            return "None"
        
        formatted = []
        for step in steps:
            formatted.append(f"Step {step.step_number}:")
            formatted.append(f"  Thought: {step.thought}")
            if step.action_type == ActionType.TOOL_CALL:
                formatted.append(f"  Action: {step.action}")
                formatted.append(f"  Action Input: {step.action_input}")
                formatted.append(f"  Observation: {step.observation}")
            
        return "\n".join(formatted)
    
    def _parse_llm_response(self, response: str, iteration: int) -> ReActStep:
        """Parse LLM response into a ReActStep with improved error handling."""

        print(f"🔍 Parsing LLM response: {repr(response[:200])}...")

        # Clean the response first
        response = response.strip()

        # Extract thought - be more flexible with the pattern
        thought_patterns = [
            r'Thought:\s*(.+?)(?=\nAction:|$)',
            r'Thought:\s*(.+?)(?=Action:|$)',
            r'^(.+?)(?=\nAction:|Action:)',
        ]

        thought = "Need to analyze the user's request"
        for pattern in thought_patterns:
            thought_match = re.search(pattern, response, re.DOTALL | re.IGNORECASE)
            if thought_match:
                thought = thought_match.group(1).strip()
                break

        # Extract action - be more flexible
        action_patterns = [
            r'Action:\s*(.+?)(?=\nAction Input:|Action Input:|$)',
            r'Action:\s*(.+?)(?=\n|$)',
        ]

        action = None
        for pattern in action_patterns:
            action_match = re.search(pattern, response, re.DOTALL | re.IGNORECASE)
            if action_match:
                action = action_match.group(1).strip()
                break

        # Extract action input
        input_patterns = [
            r'Action Input:\s*(.+?)(?=\nThought:|Thought:|Final Answer:|$)',
            r'Action Input:\s*(.+?)$',
        ]

        action_input = ""
        for pattern in input_patterns:
            input_match = re.search(pattern, response, re.DOTALL | re.IGNORECASE)
            if input_match:
                action_input = input_match.group(1).strip()
                # Remove any trailing "Final Answer" text
                action_input = re.sub(r'\s*Final Answer.*$', '', action_input, flags=re.IGNORECASE)
                break

        # Clean up action and input
        if action:
            action = action.replace('\n', ' ').strip()
            # Remove any "Final Answer" contamination from action
            action = re.sub(r'\s*Final Answer.*$', '', action, flags=re.IGNORECASE)

        action_input = action_input.replace('\n', ' ').strip()

        print(f"📝 Parsed - Thought: '{thought[:50]}...', Action: '{action}', Input: '{action_input[:50]}...'")

        # Determine action type
        if not action or action.lower() == "final answer" or "final answer" in action.lower():
            # If no clear action or it's a final answer
            final_answer = action_input if action_input else thought
            return ReActStep(
                thought=thought,
                action_type=ActionType.FINAL_ANSWER,
                action=final_answer,
                step_number=iteration
            )
        else:
            # It's a tool call
            return ReActStep(
                thought=thought,
                action_type=ActionType.TOOL_CALL,
                action=action,
                action_input=action_input,
                step_number=iteration
            )
    
    def _execute_tool(self, tool_name: str, tool_input: str) -> str:
        """Execute the specified MCP tool with given input."""

        if tool_name not in self.tools:
            return f"Error: Tool '{tool_name}' not found. Available tools: {list(self.tools.keys())}"

        try:
            tool = self.tools[tool_name]
            # Handle MCP tool wrapper
            if hasattr(tool, 'func'):
                result = tool.func(tool_input)
            elif callable(tool):
                result = tool(tool_input)
            else:
                result = str(tool)

            return str(result)

        except Exception as e:
            return f"Error executing MCP tool '{tool_name}': {str(e)}"
    
    def _update_memory_with_step(self, memory_context: str, step: ReActStep):
        """Update memory with the current reasoning step."""
        # In this implementation, we update context in the final save
        # This method can be extended for more sophisticated memory updates
        pass
    
    def _save_conversation_to_memory(self, user_input: str, final_answer: str, 
                                   reasoning_steps: List[ReActStep]):
        """Save the complete conversation to memory."""
        
        # Create reasoning summary
        reasoning_summary = self._create_reasoning_summary(reasoning_steps)
        
        # Save to memory manager
        self.memory_manager.save_context(
            {"input": user_input},
            {"output": final_answer, "reasoning": reasoning_summary}
        )
        
        print(f"\n💾 Saved conversation to memory")
        print(f"📊 Reasoning steps: {len(reasoning_steps)}")
    
    def _create_reasoning_summary(self, steps: List[ReActStep]) -> str:
        """Create a summary of the reasoning process."""
        if not steps:
            return "Direct answer without tool usage"
        
        summary_parts = []
        for step in steps:
            if step.action_type == ActionType.TOOL_CALL:
                summary_parts.append(f"Used {step.action} tool")
            
        return f"Reasoning process: {' → '.join(summary_parts)}" if summary_parts else "Direct reasoning"
