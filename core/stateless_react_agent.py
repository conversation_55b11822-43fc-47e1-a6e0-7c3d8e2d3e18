"""
Stateless ReAct Agent Implementation

Implements the ReAct (Reasoning + Acting) pattern without persistent memory:
User Input → Agent Reasoning → Action → Tool Execution →
Observation → Final Answer (no memory persistence)

Key differences from stateful agents:
- No persistent memory storage
- Each interaction is independent
- Suitable for distributed/serverless environments
- Optional context can be passed per request
"""
import re
from typing import Dict, Any, List, Optional
from dataclasses import dataclass
from enum import Enum

from logger.get_logger import log


class ActionType(Enum):
    """Types of actions the agent can take."""
    TOOL_CALL = "tool_call"
    FINAL_ANSWER = "final_answer"


@dataclass
class ReActStep:
    """Represents a single step in the ReAct reasoning process."""
    thought: str
    action_type: ActionType
    action: Optional[str] = None
    action_input: Optional[str] = None
    observation: Optional[str] = None
    step_number: int = 0


class StatelessReActAgent:
    """
    Stateless ReAct Agent that processes each interaction independently.

    Benefits:
    - No persistent state management
    - Suitable for distributed systems
    - Easy to scale horizontally
    - Simpler deployment
    - Thread-safe by design

    Compatible with existing ReAct agent interface for easy swapping.
    """

    def __init__(self, llm, tools: Dict[str, Any], max_iterations: int = 5):
        """
        Initialize Stateless ReAct Agent.

        Args:
            llm: Language model instance
            tools: Dictionary of available tools {name: tool_object}
            max_iterations: Maximum reasoning iterations
        """
        self.llm = llm
        self.tools = tools
        self.max_iterations = max_iterations

        # Create tool descriptions for prompts
        self.tool_descriptions = self._create_tool_descriptions()
        self.system_prompt = self._build_system_prompt()

    def _create_tool_descriptions(self) -> str:
        """Create formatted tool descriptions for prompts."""
        descriptions = []
        for name, tool in self.tools.items():
            if hasattr(tool, 'description'):
                desc = tool.description
            elif hasattr(tool, '__doc__') and tool.__doc__:
                desc = tool.__doc__.strip().split('\n')[0]
            else:
                desc = f"Tool: {name}"
            descriptions.append(f"- {name}: {desc}")
        return "\n".join(descriptions)

    def _build_system_prompt(self) -> str:
        """Build the system prompt for the ReAct agent."""
        return f"""You are a helpful AI assistant that uses the ReAct (Reasoning + Acting) pattern.

Available tools:
{self.tool_descriptions}

Instructions:
1. Think step by step about the user's question
2. If you need to use a tool, format your response as:
   Thought: [your reasoning]
   Action: [tool_name]
   Input: [tool_input]

3. If you can answer directly, format your response as:
   Thought: [your reasoning]
   Action: Final Answer
   Input: [your answer]

4. Always start with "Thought:" to show your reasoning
5. Be concise but thorough in your responses
6. Use tools when you need current information or calculations"""

    @log
    def process(self, user_input: str, context: Optional[str] = None) -> str:
        """
        Main processing method implementing the stateless ReAct flow.

        Args:
            user_input: User's input/question
            context: Optional context for this specific interaction

        Returns:
            Final answer from the agent
        """
        print(f"\n🤖 Stateless ReAct Agent Processing: {user_input}")
        print("=" * 60)

        if context:
            print(f"📝 Using provided context: {context[:100]}...")

        reasoning_steps: List[ReActStep] = []

        for iteration in range(1, self.max_iterations + 1):
            print(f"\n🔄 Iteration {iteration}/{self.max_iterations}")
            print("-" * 30)

            # Build prompt for current iteration
            current_prompt = self._build_iteration_prompt(user_input, reasoning_steps, context)

            # Get LLM response
            llm_response = self.llm.invoke(current_prompt)
            response_text = getattr(llm_response, 'content', str(llm_response)).strip()

            # Parse the LLM's response
            step = self._parse_llm_response(response_text, iteration)
            reasoning_steps.append(step)

            print(f"💭 Thought: {step.thought}")

            # Handle final answer
            if step.action_type == ActionType.FINAL_ANSWER:
                print(f"✅ Final Answer: {step.action}")
                return step.action

            # Handle tool call
            if step.action_type == ActionType.TOOL_CALL:
                print(f"🔧 Action: {step.action}")
                print(f"📝 Input: {step.action_input}")

                observation = self._execute_tool(step.action, step.action_input)
                step.observation = observation
                print(f"👁️ Observation: {observation}")

        # If max iterations reached
        final_answer = "I've reached the maximum number of reasoning steps. Let me provide the best answer I can based on the information gathered."
        print(f"⚠️ Max iterations reached. Providing final answer: {final_answer}")
        return final_answer

    def _build_iteration_prompt(self, user_input: str, steps: List[ReActStep], context: Optional[str] = None) -> str:
        """Build the prompt for each iteration of the reasoning loop."""
        # Build reasoning history
        reasoning_history = []
        for step in steps:
            reasoning_history.append(f"Thought: {step.thought}")
            if step.action_type == ActionType.TOOL_CALL:
                reasoning_history.append(f"Action: {step.action}")
                reasoning_history.append(f"Input: {step.action_input}")
            if step.observation:
                reasoning_history.append(f"Observation: {step.observation}")

        # Combine all parts
        prompt_parts = [self.system_prompt]

        if context:
            prompt_parts.append(f"\nContext: {context}")

        if reasoning_history:
            prompt_parts.append(f"\nPrevious reasoning:\n" + "\n".join(reasoning_history))

        prompt_parts.append(f"\nQuestion: {user_input}")

        return "\n".join(prompt_parts)

    def _parse_llm_response(self, response: str, iteration: int) -> ReActStep:
        """Parse the LLM's raw output to determine the thought, action, and input."""
        print(f"🔍 Parsing LLM Response:\n---\n{response}\n---")

        # Extract thought
        thought_match = re.search(r"Thought:\s*(.*?)(?=\nAction:|$)", response, re.DOTALL)
        thought = thought_match.group(1).strip() if thought_match else "No thought provided."

        # Extract action
        action_match = re.search(r"Action:\s*(.+?)(?=\nInput:|$)", response, re.DOTALL)
        action = action_match.group(1).strip() if action_match else None

        # Extract input
        input_match = re.search(r"Input:\s*(.*?)$", response, re.DOTALL)
        action_input = input_match.group(1).strip() if input_match else ""

        # Determine action type
        if action and action.lower() in ["final answer", "final_answer"]:
            return ReActStep(
                thought=thought,
                action_type=ActionType.FINAL_ANSWER,
                action=action_input,  # The final answer is in the input field
                step_number=iteration
            )
        elif action and action in self.tools:
            return ReActStep(
                thought=thought,
                action_type=ActionType.TOOL_CALL,
                action=action,
                action_input=action_input,
                step_number=iteration
            )
        else:
            # Fallback: treat as final answer
            return ReActStep(
                thought=thought,
                action_type=ActionType.FINAL_ANSWER,
                action=response,  # Use the entire response as final answer
                step_number=iteration
            )

    def _execute_tool(self, tool_name: str, tool_input: str) -> str:
        """Execute the specified tool with given input."""
        if tool_name not in self.tools:
            return f"Error: Tool '{tool_name}' not found. Available tools: {list(self.tools.keys())}"

        try:
            tool = self.tools[tool_name]
            # Handle MCP tool wrapper
            if hasattr(tool, 'func'):
                result = tool.func(tool_input)
            elif callable(tool):
                result = tool(tool_input)
            else:
                result = str(tool)

            return str(result)

        except Exception as e:
            error_msg = f"Error executing tool '{tool_name}': {str(e)}"
            print(f"❌ {error_msg}")
            return error_msg

    def get_tool_names(self) -> List[str]:
        """Get list of available tool names."""
        return list(self.tools.keys())

    def get_tool_descriptions(self) -> str:
        """Get formatted descriptions of all tools."""
        return self.tool_descriptions


# Compatibility functions for easy integration
def create_stateless_agent(llm, tools: Dict[str, Any], max_iterations: int = 5) -> StatelessReActAgent:
    """
    Factory function to create a stateless ReAct agent.

    Args:
        llm: Language model instance
        tools: Dictionary of available tools
        max_iterations: Maximum reasoning iterations

    Returns:
        StatelessReActAgent instance
    """
    return StatelessReActAgent(llm, tools, max_iterations)