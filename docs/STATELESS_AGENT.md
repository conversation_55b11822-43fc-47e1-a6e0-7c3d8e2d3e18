# Stateless ReAct Agent

## Overview

The Stateless ReAct Agent is a new implementation of the ReAct (Reasoning + Acting) pattern that processes each interaction independently without persistent memory storage. This design makes it ideal for distributed systems, serverless environments, and scenarios requiring high scalability.

## Key Differences from Stateful Agents

| Feature | Stateful Agent | Stateless Agent |
|---------|----------------|-----------------|
| **Memory** | Persistent SQLite database | No persistent storage |
| **State** | Maintains conversation history | Each interaction is independent |
| **Scalability** | Limited by memory management | Easily scalable horizontally |
| **Deployment** | Requires persistent storage | Suitable for serverless/containers |
| **Thread Safety** | Requires careful memory handling | Thread-safe by design |
| **Context** | Automatic from previous conversations | Optional per-request context |

## Architecture

```
User Input + Optional Context
           ↓
    Stateless ReAct Agent
           ↓
    Reasoning Loop (1-N iterations):
    ┌─────────────────────────────────┐
    │ 1. Build Prompt with Context    │
    │ 2. LLM Reasoning                │
    │ 3. Parse Response               │
    │ 4. Execute Tool (if needed)     │
    │ 5. Observe Results              │
    └─────────────────────────────────┘
           ↓
      Final Answer
```

## Usage Examples

### Basic Usage

```python
from core.stateless_react_agent import StatelessReActAgent
from mcps.tools_manager import MCPToolsManager
from models import get_model_provider
from config.config import Config

# Initialize components
config = Config()
llm = get_model_provider(config.model_provider).get_llm()
tools = MCPToolsManager().get_tools()

# Create stateless agent
agent = StatelessReActAgent(llm=llm, tools=tools, max_iterations=5)

# Process requests independently
result1 = agent.process("What's the current date?")
result2 = agent.process("Calculate 15 + 27")
result3 = agent.process("What time is it?")
```

### Using Context

```python
# Provide context for specific interactions
context = "The user is a student learning mathematics."
result = agent.process("What's 25 * 4?", context=context)

# Different context for the same question
business_context = "The user is calculating quarterly revenue."
result = agent.process("What's 25 * 4?", context=business_context)
```

### Factory Function

```python
from core.stateless_react_agent import create_stateless_agent

# Quick creation
agent = create_stateless_agent(llm, tools, max_iterations=3)
result = agent.process("Search for Python tutorials")
```

## Benefits

### 1. **Scalability**
- No shared state between requests
- Easy horizontal scaling
- Suitable for load-balanced environments
- Perfect for microservices architecture

### 2. **Deployment Flexibility**
- **Serverless Functions**: AWS Lambda, Azure Functions, Google Cloud Functions
- **Containers**: Docker, Kubernetes
- **Edge Computing**: Deploy closer to users
- **CDN Integration**: Process requests at edge locations

### 3. **Thread Safety**
- Multiple concurrent requests without state conflicts
- No need for complex locking mechanisms
- Parallel processing capabilities

### 4. **Simplicity**
- No database setup or maintenance
- Easier testing and debugging
- Reduced infrastructure complexity
- Faster cold starts

### 5. **Reliability**
- No memory corruption issues
- Each request is isolated
- Failure in one request doesn't affect others

## Use Cases

### ✅ Ideal For:
- **API Services**: RESTful APIs, GraphQL endpoints
- **Chatbots**: Stateless chat interfaces
- **Batch Processing**: Processing multiple independent requests
- **Microservices**: Service-to-service communication
- **Edge Computing**: Low-latency processing at edge locations
- **A/B Testing**: Easy to deploy different versions

### ❌ Not Ideal For:
- **Long Conversations**: Multi-turn dialogues requiring context
- **Learning Systems**: Agents that need to remember user preferences
- **Session-based Applications**: Applications requiring user session state
- **Complex Workflows**: Multi-step processes spanning multiple requests

## Comparison with Existing Agents

### Performance Comparison

```python
# Test all three agent types
from test_stateless_agent import compare_agents
compare_agents()
```

### Memory Usage

| Agent Type | Memory Usage | Startup Time | Scalability |
|------------|--------------|--------------|-------------|
| **ReAct Agent** | High (SQLite + history) | Slow | Limited |
| **Gemini ReAct Agent** | High (SQLite + history) | Slow | Limited |
| **Stateless ReAct Agent** | Low (no persistence) | Fast | Excellent |

## Configuration

The stateless agent uses the same configuration as other agents but doesn't require memory-related settings:

```python
# No memory manager needed
agent = StatelessReActAgent(
    llm=llm,
    tools=tools,
    max_iterations=5  # Adjust based on complexity needs
)
```

## Integration with Existing Code

The stateless agent is designed to be a drop-in replacement for stateful agents in many scenarios:

```python
# Before (stateful)
from core import ReActAgent
from memory import ReActMemoryManager

memory = ReActMemoryManager("memory.db")
agent = ReActAgent(llm, tools, memory, max_iterations=5)
result = agent.process(user_input)

# After (stateless)
from core import StatelessReActAgent

agent = StatelessReActAgent(llm, tools, max_iterations=5)
result = agent.process(user_input, context=optional_context)
```

## Testing

Run the comprehensive test suite:

```bash
python test_stateless_agent.py
```

The test suite covers:
- ✅ Basic functionality
- ✅ Tool integration
- ✅ Context handling
- ✅ Thread safety
- ✅ Performance comparison
- ✅ Error handling

## Best Practices

### 1. **Context Management**
```python
# Provide relevant context for better results
context = f"User preferences: {user_prefs}, Session info: {session_data}"
result = agent.process(query, context=context)
```

### 2. **Error Handling**
```python
try:
    result = agent.process(user_input)
except Exception as e:
    # Handle errors gracefully
    result = f"I apologize, but I encountered an error: {str(e)}"
```

### 3. **Iteration Limits**
```python
# Adjust max_iterations based on query complexity
simple_agent = StatelessReActAgent(llm, tools, max_iterations=2)  # For simple queries
complex_agent = StatelessReActAgent(llm, tools, max_iterations=7)  # For complex reasoning
```

### 4. **Tool Selection**
```python
# Use specific tool sets for different use cases
math_tools = {k: v for k, v in all_tools.items() if 'math' in k or 'calc' in k}
math_agent = StatelessReActAgent(llm, math_tools, max_iterations=3)
```

## Future Enhancements

- **Caching Layer**: Add optional response caching for repeated queries
- **Context Templates**: Pre-defined context templates for common scenarios
- **Streaming Responses**: Support for streaming responses in real-time
- **Metrics Collection**: Built-in metrics for performance monitoring
- **Rate Limiting**: Built-in rate limiting for production deployments

## Contributing

When contributing to the stateless agent:

1. Maintain compatibility with the existing agent interface
2. Ensure thread safety in all operations
3. Add comprehensive tests for new features
4. Update documentation for any API changes
5. Consider performance implications of changes
