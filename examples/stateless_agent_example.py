#!/usr/bin/env python3
"""
Stateless ReAct Agent Example

This example demonstrates how to use the StatelessReActAgent with your existing
tools and configuration setup. It shows the key differences and benefits
compared to stateful agents.
"""
import os
from dotenv import load_dotenv

# Load environment variables
if os.path.exists("../.env"):
    load_dotenv("../.env")
elif os.path.exists(".env"):
    load_dotenv()


def basic_example():
    """Basic usage example of the stateless agent."""
    print("🚀 BASIC STATELESS AGENT EXAMPLE")
    print("=" * 50)
    
    try:
        from config.config import Config
        from models import get_model_provider
        from mcps.tools_manager import MCPToolsManager
        from core.stateless_react_agent import StatelessReActAgent
        
        # Initialize components (same as stateful agents)
        print("🔧 Initializing components...")
        config = Config()
        model_provider = get_model_provider(config.model_provider)
        llm = model_provider.get_llm()
        
        tools_manager = MCPToolsManager()
        tools = tools_manager.get_tools()
        
        # Create stateless agent (no memory manager needed!)
        agent = StatelessReActAgent(
            llm=llm,
            tools=tools,
            max_iterations=3
        )
        
        print(f"✅ Stateless agent created with {len(tools)} tools")
        
        # Example queries
        queries = [
            "What's the current date?",
            "Calculate 25 + 17",
            "What time is it now?",
        ]
        
        print("\n📝 Processing queries independently...")
        for i, query in enumerate(queries, 1):
            print(f"\n🔍 Query {i}: {query}")
            print("-" * 30)
            
            result = agent.process(query)
            print(f"✅ Result: {result}")
        
        return True
        
    except Exception as e:
        print(f"❌ Basic example failed: {e}")
        return False


def context_example():
    """Example showing how to use context with stateless agent."""
    print("\n🎯 CONTEXT USAGE EXAMPLE")
    print("=" * 50)
    
    try:
        from config.config import Config
        from models import get_model_provider
        from mcps.tools_manager import MCPToolsManager
        from core.stateless_react_agent import StatelessReActAgent
        
        # Initialize components
        config = Config()
        model_provider = get_model_provider(config.model_provider)
        llm = model_provider.get_llm()
        
        tools_manager = MCPToolsManager()
        tools = tools_manager.get_tools()
        
        agent = StatelessReActAgent(llm=llm, tools=tools, max_iterations=3)
        
        # Same question with different contexts
        question = "What's 12 * 8?"
        
        contexts = [
            ("Student Context", "The user is a 5th grade student learning multiplication tables."),
            ("Business Context", "The user is calculating monthly revenue projections."),
            ("Programming Context", "The user is optimizing algorithm performance calculations."),
        ]
        
        print(f"📝 Question: {question}")
        print("\n🔄 Processing with different contexts...")
        
        for context_name, context in contexts:
            print(f"\n📋 {context_name}:")
            print(f"   Context: {context}")
            
            result = agent.process(question, context=context)
            print(f"✅ Result: {result}")
        
        return True
        
    except Exception as e:
        print(f"❌ Context example failed: {e}")
        return False


def comparison_example():
    """Example comparing stateless vs stateful agent behavior."""
    print("\n⚖️ STATELESS VS STATEFUL COMPARISON")
    print("=" * 50)
    
    try:
        from config.config import Config
        from models import get_model_provider
        from mcps.tools_manager import MCPToolsManager
        from memory import ReActMemoryManager
        from core import ReActAgent
        from core.stateless_react_agent import StatelessReActAgent
        
        # Initialize common components
        config = Config()
        model_provider = get_model_provider(config.model_provider)
        llm = model_provider.get_llm()
        
        tools_manager = MCPToolsManager()
        tools = tools_manager.get_tools()
        
        # Create both agents
        memory = ReActMemoryManager("example_memory.db")
        stateful_agent = ReActAgent(llm=llm, tools=tools, memory_manager=memory, max_iterations=3)
        stateless_agent = StatelessReActAgent(llm=llm, tools=tools, max_iterations=3)
        
        # Test sequence
        questions = [
            "What's the current date?",
            "Remember that date for later",
            "What date did I ask about before?",
        ]
        
        print("🔄 Testing conversation memory...")
        
        print("\n📊 Stateful Agent (with memory):")
        for i, question in enumerate(questions, 1):
            print(f"  Q{i}: {question}")
            try:
                result = stateful_agent.process(question)
                print(f"  A{i}: {result[:80]}...")
            except Exception as e:
                print(f"  A{i}: Error - {e}")
        
        print("\n📊 Stateless Agent (no memory):")
        for i, question in enumerate(questions, 1):
            print(f"  Q{i}: {question}")
            try:
                result = stateless_agent.process(question)
                print(f"  A{i}: {result[:80]}...")
            except Exception as e:
                print(f"  A{i}: Error - {e}")
        
        # Clean up
        memory.clear()
        
        print("\n💡 Key Observations:")
        print("   • Stateful agent can reference previous conversations")
        print("   • Stateless agent treats each query independently")
        print("   • Stateless agent is more predictable and scalable")
        print("   • Stateful agent is better for conversational flows")
        
        return True
        
    except Exception as e:
        print(f"❌ Comparison example failed: {e}")
        return False


def factory_function_example():
    """Example using the factory function for quick setup."""
    print("\n🏭 FACTORY FUNCTION EXAMPLE")
    print("=" * 50)
    
    try:
        from config.config import Config
        from models import get_model_provider
        from mcps.tools_manager import MCPToolsManager
        from core.stateless_react_agent import create_stateless_agent
        
        # Initialize components
        config = Config()
        llm = get_model_provider(config.model_provider).get_llm()
        tools = MCPToolsManager().get_tools()
        
        # Quick agent creation using factory function
        agent = create_stateless_agent(llm, tools, max_iterations=2)
        
        print("✅ Agent created using factory function")
        print(f"🔧 Available tools: {len(agent.get_tool_names())}")
        
        # Quick test
        result = agent.process("What's 5 + 3?")
        print(f"✅ Quick test result: {result}")
        
        return True
        
    except Exception as e:
        print(f"❌ Factory function example failed: {e}")
        return False


def performance_example():
    """Example showing performance characteristics."""
    print("\n⚡ PERFORMANCE CHARACTERISTICS")
    print("=" * 50)
    
    try:
        import time
        from config.config import Config
        from models import get_model_provider
        from mcps.tools_manager import MCPToolsManager
        from core.stateless_react_agent import StatelessReActAgent
        
        # Initialize components
        config = Config()
        llm = get_model_provider(config.model_provider).get_llm()
        tools = MCPToolsManager().get_tools()
        
        # Create agent
        agent = StatelessReActAgent(llm=llm, tools=tools, max_iterations=2)
        
        # Test multiple independent requests
        questions = [
            "What's 10 + 5?",
            "What's 20 - 8?",
            "What's 6 * 7?",
            "What's 100 / 4?",
        ]
        
        print("🔄 Processing multiple independent requests...")
        
        start_time = time.time()
        results = []
        
        for i, question in enumerate(questions, 1):
            print(f"  Processing request {i}: {question}")
            result = agent.process(question)
            results.append(result)
            print(f"  ✅ Result {i}: {result}")
        
        end_time = time.time()
        total_time = end_time - start_time
        
        print(f"\n📊 Performance Summary:")
        print(f"   • Total requests: {len(questions)}")
        print(f"   • Total time: {total_time:.2f} seconds")
        print(f"   • Average time per request: {total_time/len(questions):.2f} seconds")
        print(f"   • All requests processed independently")
        print(f"   • No memory overhead between requests")
        
        return True
        
    except Exception as e:
        print(f"❌ Performance example failed: {e}")
        return False


def main():
    """Run all examples."""
    print("🎯 STATELESS REACT AGENT EXAMPLES")
    print("=" * 60)
    
    examples = [
        ("Basic Usage", basic_example),
        ("Context Usage", context_example),
        ("Stateless vs Stateful", comparison_example),
        ("Factory Function", factory_function_example),
        ("Performance", performance_example),
    ]
    
    results = []
    
    for name, example_func in examples:
        print(f"\n🔍 Running {name} Example...")
        try:
            success = example_func()
            results.append(success)
            if success:
                print(f"✅ {name} example completed successfully")
            else:
                print(f"❌ {name} example failed")
        except Exception as e:
            print(f"❌ {name} example error: {e}")
            results.append(False)
    
    # Summary
    print(f"\n📊 EXAMPLES SUMMARY")
    print("=" * 30)
    passed = sum(results)
    total = len(results)
    print(f"✅ Examples passed: {passed}/{total}")
    
    if passed == total:
        print("🎉 All examples completed successfully!")
        print("\n💡 Next Steps:")
        print("   • Try the test suite: python test_stateless_agent.py")
        print("   • Read the documentation: docs/STATELESS_AGENT.md")
        print("   • Integrate into your applications")
    else:
        print("⚠️ Some examples failed. Check the output above.")


if __name__ == "__main__":
    main()
